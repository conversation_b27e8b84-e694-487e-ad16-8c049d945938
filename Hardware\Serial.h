#ifndef __SERIAL_H
#define __SERIAL_H

#include <stdint.h>

// 数据包相关定义
#define PACKET_SIZE 6
#define PACKET_HEADER 0xFF
#define PACKET_FOOTER 0xFE

// 串口接收状态
typedef enum {
    RX_STATE_IDLE = 0,
    RX_STATE_HEADER,
    RX_STATE_DATA_HIGH,
    RX_STATE_DATA_LOW,
    RX_STATE_RESERVED1,
    RX_STATE_RESERVED2,
    RX_STATE_FOOTER
} RxState_t;

// 函数声明
void Serial_Init(void);
void Serial_SendByte(uint8_t Byte);
void Serial_SendArray(uint8_t *Array, uint16_t Length);
void Serial_SendString(char *String);
void Serial_SendNumber(uint32_t Number, uint8_t Length);
uint8_t Serial_GetRxFlag(void);
int16_t Serial_GetReceivedData(void);
void Serial_ClearRxFlag(void);

#endif
