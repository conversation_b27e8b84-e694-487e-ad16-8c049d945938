# 测试程序说明

## 测试步骤

### 1. 硬件连接测试
按照 `连接说明.md` 正确连接所有硬件后：

1. **OLED测试**：
   - 给STM32上电
   - OLED应该显示：
     ```
     OpenMV Tracker
     X Diff:
     Status:
     Waiting...
     ```

2. **串口连接测试**：
   - 确保OpenMV与STM32串口连接正确
   - PA9(STM32) → P4(OpenMV)
   - PA10(STM32) → P5(OpenMV)

### 2. OpenMV程序测试

1. **运行OpenMV程序**：
   - 将 `openmv_black_detection.py` 保存到OpenMV
   - 运行程序，观察串口输出

2. **黑色物体检测**：
   - 在摄像头前放置黑色物体（如黑色纸张、黑色笔等）
   - 观察OpenMV IDE中的图像显示
   - 应该看到红色边框围绕黑色物体
   - 绿色十字标记物体中心

### 3. 系统联调测试

1. **数据传输测试**：
   - OpenMV检测到黑色物体时，STM32的OLED应显示x值差
   - 移动黑色物体，观察OLED上数值变化

2. **状态显示测试**：
   - 物体在视野左侧：显示 "Left" 和负数
   - 物体在视野右侧：显示 "Right" 和正数  
   - 无物体时：显示 "No Blob" 和 0

### 4. 预期结果

#### OLED显示示例：
```
物体在左侧时：
OpenMV Tracker
X Diff: -85
Status: Left
Blob->Left

物体在右侧时：
OpenMV Tracker  
X Diff: 120
Status: Right
Blob->Right

无物体时：
OpenMV Tracker
X Diff: 0
Status: No Blob
No Detection
```

#### OpenMV串口输出示例：
```
OpenMV黑色色块识别程序启动
视野中心x坐标: 160
黑色阈值: (0, 30, -128, 127, -128, 127)
最小色块面积: 100
发送数据: x_diff = -85 数据包: ['0xff', '0xff', '0xab', '0x0', '0x0', '0xfe']
FPS: 18.5
```

## 调试技巧

### 1. 如果OLED无显示
- 检查电源连接
- 用万用表测试I2C连接
- 确认代码正确烧录

### 2. 如果检测不到黑色物体
- 调整光照条件
- 修改黑色阈值参数
- 确保物体足够大（>100像素）

### 3. 如果串口无数据
- 检查串口连接
- 确认波特率设置
- 查看OpenMV程序是否正常运行

### 4. 数据包格式验证
数据包应为6字节：
- 字节1: 0xFF (包头)
- 字节2: 数据高字节
- 字节3: 数据低字节  
- 字节4: 0x00 (保留)
- 字节5: 0x00 (保留)
- 字节6: 0xFE (包尾)

## 性能指标

- **检测帧率**: ~20 FPS
- **串口波特率**: 9600 bps
- **检测精度**: ±1像素
- **响应延迟**: <100ms
- **检测范围**: x坐标差值 -320 到 +320

## 故障排除清单

- [ ] 硬件连接正确
- [ ] 电源供电正常
- [ ] STM32程序烧录成功
- [ ] OpenMV程序运行正常
- [ ] OLED显示正常
- [ ] 串口通信正常
- [ ] 黑色物体检测正常
- [ ] 数据显示正确
