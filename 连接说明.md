# OpenMV + STM32 + OLED 黑色色块识别系统连接说明

## 硬件连接

### 1. STM32F103C8T6 与 OLED (0.96寸 I2C接口)
```
STM32F103C8T6    →    OLED
VCC (3.3V)       →    VCC
GND              →    GND
PB8              →    SCL (时钟线)
PB9              →    SDA (数据线)
```

### 2. STM32F103C8T6 与 OpenMV
```
STM32F103C8T6    →    OpenMV
VCC (3.3V)       →    VCC
GND              →    GND
PA9 (USART1_TX)  →    P4 (UART3_RX)
PA10 (USART1_RX) →    P5 (UART3_TX)
```

### 3. 完整连接图
```
OpenMV H7
┌─────────────┐
│  P4 (RX) ●──┼──→ PA9 (TX)
│  P5 (TX) ●──┼──→ PA10 (RX)
│  VCC     ●──┼──→ 3.3V
│  GND     ●──┼──→ GND
└─────────────┘
       │
       │
STM32F103C8T6
┌─────────────┐
│  PA9     ●  │
│  PA10    ●  │
│  PB8     ●──┼──→ SCL
│  PB9     ●──┼──→ SDA
│  3.3V    ●  │    │
│  GND     ●  │    │
└─────────────┘    │
                   │
            OLED 0.96"
            ┌─────────┐
            │  VCC ●  │
            │  GND ●  │
            │  SCL ●  │
            │  SDA ●  │
            └─────────┘
```

## 软件配置

### 1. STM32 程序功能
- 串口接收OpenMV发送的数据包
- 解析数据包获取x值差
- 在OLED上显示检测结果
- 数据包格式：0xFF + 高字节 + 低字节 + 保留1 + 保留2 + 0xFE

### 2. OpenMV 程序功能
- 识别黑色色块
- 计算色块中心与视野中心的x坐标差值
- 通过串口发送数据到STM32
- LED指示检测状态

### 3. OLED 显示内容
```
行1: OpenMV Tracker
行2: X Diff: [数值]
行3: Status: [Left/Right/No Blob]
行4: [详细状态信息]
```

## 使用步骤

### 1. 硬件准备
1. 按照连接图正确连接所有硬件
2. 确保所有连接牢固，无短路
3. 检查电源连接（3.3V和GND）

### 2. 软件烧录
1. 将STM32程序烧录到STM32F103C8T6
2. 将OpenMV程序保存到OpenMV的SD卡或内部存储

### 3. 系统测试
1. 给STM32上电，OLED应显示初始界面
2. 给OpenMV上电并运行程序
3. 在OpenMV摄像头前放置黑色物体
4. 观察OLED显示的x值差变化

## 参数说明

### OpenMV 参数
- **分辨率**: QVGA (320x240)
- **视野中心**: x=160
- **黑色阈值**: (0, 30, -128, 127, -128, 127)
- **最小色块面积**: 100像素
- **串口波特率**: 9600

### STM32 参数
- **串口**: USART1
- **波特率**: 9600
- **数据位**: 8
- **停止位**: 1
- **校验位**: 无

## 故障排除

### 1. OLED无显示
- 检查I2C连接（PB8→SCL, PB9→SDA）
- 检查电源连接（3.3V和GND）
- 确认OLED地址为0x78

### 2. 串口无数据
- 检查串口连接（PA9→P4, PA10→P5）
- 确认波特率设置为9600
- 检查OpenMV程序是否正常运行

### 3. 检测不准确
- 调整黑色阈值参数
- 改善光照条件
- 增大最小色块面积阈值

## 扩展功能

### 可以添加的功能
1. 多色块检测
2. 色块大小信息传输
3. 图像处理参数动态调整
4. 更复杂的控制算法

### 硬件扩展
1. 添加舵机控制
2. 增加蜂鸣器提示
3. 添加按键控制
4. 扩展更多传感器
