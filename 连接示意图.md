# 模块连接示意图

## 整体连接图

```
                    ┌─────────────────────────────────┐
                    │           STM32F103             │
                    │                                 │
                    │  PA9 ●────────────────────────┐ │
                    │  PA10●────────────────────────┼─┼─┐
                    │                               │ │ │
                    │  PB6 ●──────────────────────┐ │ │ │
                    │  PB7 ●──────────────────────┼─┼─┼─┼─┐
                    │                             │ │ │ │ │
                    │  3.3V●──────────────────────┼─┼─┼─┼─┼─┐
                    │  GND ●──────────────────────┼─┼─┼─┼─┼─┼─┐
                    └─────────────────────────────┼─┼─┼─┼─┼─┼─┼─┘
                                                  │ │ │ │ │ │ │
                    ┌─────────────────────────────┼─┼─┘ │ │ │ │
                    │         OpenMV              │ │   │ │ │ │
                    │                             │ │   │ │ │ │
                    │  P4 ●───────────────────────┘ │   │ │ │ │
                    │  P5 ●─────────────────────────┘   │ │ │ │
                    │                                   │ │ │ │
                    │  GND●─────────────────────────────┼─┼─┼─┘
                    └───────────────────────────────────┼─┼─┼───┘
                                                        │ │ │
                    ┌───────────────────────────────────┼─┼─┘
                    │         OLED 0.96"                │ │
                    │                                   │ │
                    │  SCL●─────────────────────────────┘ │
                    │  SDA●───────────────────────────────┘
                    │  VCC●───────────────────────────────┘
                    │  GND●───────────────────────────────┘
                    └─────────────────────────────────────┘
```

## 详细连接对照表

### 连接线颜色建议
```
功能          推荐颜色    STM32引脚    目标模块引脚
──────────────────────────────────────────────
串口发送      黄色        PA9         OpenMV P4
串口接收      绿色        PA10        OpenMV P5
I2C时钟       蓝色        PB6         OLED SCL
I2C数据       紫色        PB7         OLED SDA
电源正极      红色        3.3V        OLED VCC
公共地线      黑色        GND         所有模块GND
```

## 面包板连接图

```
面包板布局示例：

    A  B  C  D  E     F  G  H  I  J
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 1
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 2
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 3
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 4
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 5
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 6
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 7
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 8
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 9
    ●  ●  ●  ●  ●     ●  ●  ●  ●  ●  ← 10
    ═══════════════════════════════════
    +  +  +  +  +     +  +  +  +  +  ← 电源正极排
    -  -  -  -  -     -  -  -  -  -  ← 电源负极排

连接方案：
- 电源正极排：连接3.3V
- 电源负极排：连接GND
- A1-A4：OLED模块插入
- F1-F4：连接到STM32的I2C引脚
- A6-A8：OpenMV串口连接点
- F6-F8：连接到STM32的USART引脚
```

## 实际连接步骤图解

### 步骤1：STM32与OLED连接
```
STM32开发板                    OLED显示屏
┌─────────────┐               ┌─────────────┐
│             │               │  ●VCC       │
│        PB6 ●┼───────────────┼● SCL        │
│        PB7 ●┼───────────────┼● SDA        │
│       3.3V ●┼───────────────┼● VCC        │
│        GND ●┼───────────────┼● GND        │
│             │               │             │
└─────────────┘               └─────────────┘
```

### 步骤2：STM32与OpenMV连接
```
STM32开发板                    OpenMV摄像头
┌─────────────┐               ┌─────────────┐
│             │               │             │
│        PA9 ●┼───────────────┼● P4         │
│       PA10 ●┼───────────────┼● P5         │
│        GND ●┼───────────────┼● GND        │
│             │               │             │
└─────────────┘               └─────────────┘
```

### 步骤3：完整系统连接
```
     OpenMV                STM32               OLED
┌─────────────┐      ┌─────────────┐      ┌─────────────┐
│             │      │             │      │             │
│  P4 ●───────┼──────┼● PA9        │      │             │
│  P5 ●───────┼──────┼● PA10       │      │             │
│             │      │        PB6 ●┼──────┼● SCL        │
│             │      │        PB7 ●┼──────┼● SDA        │
│             │      │       3.3V ●┼──────┼● VCC        │
│ GND ●───────┼──────┼● GND        │      │             │
│             │      │        GND ●┼──────┼● GND        │
└─────────────┘      └─────────────┘      └─────────────┘
```

## 引脚功能说明

### STM32F103 关键引脚
```
引脚    │ 功能        │ 复用功能      │ 连接说明
────────┼────────────┼─────────────┼──────────────
PA9     │ GPIO       │ USART1_TX   │ 串口发送引脚
PA10    │ GPIO       │ USART1_RX   │ 串口接收引脚
PB6     │ GPIO       │ 软件I2C_SCL  │ I2C时钟引脚（软件模拟）
PB7     │ GPIO       │ 软件I2C_SDA  │ I2C数据引脚（软件模拟）
3.3V    │ 电源输出    │ -           │ 为OLED供电
GND     │ 地线       │ -           │ 公共参考地
```

### OpenMV H7 关键引脚
```
引脚    │ 功能        │ 复用功能      │ 连接说明
────────┼────────────┼─────────────┼──────────────
P4      │ GPIO       │ UART3_RX    │ 串口接收引脚
P5      │ GPIO       │ UART3_TX    │ 串口发送引脚
VIN     │ 电源输入    │ -           │ 5V电源输入
GND     │ 地线       │ -           │ 公共参考地
```

### OLED显示屏引脚
```
引脚    │ 功能        │ 电压        │ 连接说明
────────┼────────────┼────────────┼──────────────
VCC     │ 电源正极    │ 3.3V       │ 连接STM32的3.3V
GND     │ 电源负极    │ 0V         │ 连接公共地线
SCL     │ I2C时钟    │ 3.3V逻辑   │ 连接STM32的PB6
SDA     │ I2C数据    │ 3.3V逻辑   │ 连接STM32的PB7
```

## 连接检查清单

### 连接前检查
- [ ] 所有模块已断电
- [ ] 杜邦线完好无损
- [ ] 引脚定义已确认
- [ ] 连接表已准备

### 连接中检查
- [ ] STM32 PA9 → OpenMV P4
- [ ] STM32 PA10 → OpenMV P5
- [ ] STM32 PB6 → OLED SCL
- [ ] STM32 PB7 → OLED SDA
- [ ] STM32 3.3V → OLED VCC
- [ ] 所有GND已连接

### 连接后检查
- [ ] 无短路现象
- [ ] 连接牢固可靠
- [ ] 线缆整理有序
- [ ] 标签标识清楚

## 故障排除连接图

### 串口通信异常
```
检查点1: TX/RX是否交叉连接
STM32 PA9(TX) ──→ OpenMV P4(RX) ✓
STM32 PA10(RX) ←── OpenMV P5(TX) ✓

检查点2: 是否有公共地线
STM32 GND ──── OpenMV GND ✓
```

### OLED显示异常
```
检查点1: 电源连接
STM32 3.3V ──→ OLED VCC ✓
STM32 GND ──→ OLED GND ✓

检查点2: I2C连接
STM32 PB6 ──→ OLED SCL ✓
STM32 PB7 ──→ OLED SDA ✓
```

---

**连接提示：**
1. 建议使用不同颜色的杜邦线区分功能
2. 连接完成后用标签标记每根线的功能
3. 保持连接线尽可能短，减少干扰
4. 定期检查连接是否松动
