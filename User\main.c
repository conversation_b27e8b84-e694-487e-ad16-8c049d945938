#include "stm32f10x.h"                  // Device header
#include "Delay.h"
#include "OLED.h"
#include "Serial.h"

int main(void)
{
	int16_t x_diff = 0;

	/*模块初始化*/
	OLED_Init();		//OLED初始化
	Serial_Init();		//串口初始化

	/*OLED显示初始界面*/
	OLED_ShowString(1, 1, "OpenMV Tracker");	//1行1列显示标题
	OLED_ShowString(2, 1, "X Diff:");			//2行1列显示X差值标签
	OLED_ShowString(3, 1, "Status:");			//3行1列显示状态标签
	OLED_ShowString(4, 1, "Waiting...");		//4行1列显示等待状态

	while (1)
	{
		// 检查是否接收到新数据
		if (Serial_GetRxFlag())
		{
			// 获取接收到的x值差数据
			x_diff = Serial_GetReceivedData();

			// 清除接收标志
			Serial_ClearRxFlag();

			// 在OLED上显示x值差
			OLED_ShowString(2, 8, "     ");  // 清除旧数据
			OLED_ShowSignedNum(2, 8, x_diff, 4);

			// 根据x值差显示状态
			if (x_diff == 0)
			{
				OLED_ShowString(3, 8, "No Blob ");
				OLED_ShowString(4, 1, "No Detection");
			}
			else if (x_diff > 0)
			{
				OLED_ShowString(3, 8, "Right   ");
				OLED_ShowString(4, 1, "Blob->Right ");
			}
			else
			{
				OLED_ShowString(3, 8, "Left    ");
				OLED_ShowString(4, 1, "Blob->Left  ");
			}
		}

		// 短暂延时
		Delay_ms(10);
	}
}
