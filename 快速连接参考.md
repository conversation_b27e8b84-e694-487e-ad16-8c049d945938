# 快速连接参考卡

## 🔌 一分钟连接指南

### 必需连接（5根线）
```
序号  STM32引脚    →    目标模块      →    功能
1     PA9         →    OpenMV P4     →    串口通信
2     PA10        →    OpenMV P5     →    串口通信  
3     PB6         →    OLED SCL      →    I2C时钟
4     PB7         →    OLED SDA      →    I2C数据
5     GND         →    所有模块GND    →    公共地线
```

### 电源连接（2根线）
```
序号  STM32引脚    →    目标模块      →    功能
6     3.3V        →    OLED VCC      →    OLED供电
7     USB         →    STM32         →    STM32供电
8     USB         →    OpenMV        →    OpenMV供电
```

## 📋 连接检查表

### 串口连接 ✓
- [ ] STM32 PA9 → OpenMV P4 (黄线)
- [ ] STM32 PA10 → OpenMV P5 (绿线)

### I2C连接 ✓  
- [ ] STM32 PB6 → OLED SCL (蓝线)
- [ ] STM32 PB7 → OLED SDA (紫线)

### 电源连接 ✓
- [ ] STM32 3.3V → OLED VCC (红线)
- [ ] 所有GND连接 (黑线)

## ⚡ 快速测试步骤

### 1. 硬件测试
```
步骤1: 给STM32上电 → LED亮起 ✓
步骤2: 给OpenMV上电 → LED亮起 ✓  
步骤3: OLED显示测试 → 显示内容 ✓
```

### 2. 软件测试
```
步骤1: 下载STM32程序 → OLED显示"Waiting" ✓
步骤2: 运行OpenMV测试程序 → 数据发送 ✓
步骤3: 观察OLED变化 → 显示接收数据 ✓
```

## 🚨 常见错误速查

| 现象 | 可能原因 | 解决方法 |
|------|----------|----------|
| OLED无显示 | 电源/I2C连接错误 | 检查VCC和SCL/SDA |
| 串口无数据 | TX/RX接反 | 交换PA9和PA10连接 |
| 系统重启 | 短路 | 断电检查所有连接 |
| 数据乱码 | 波特率不匹配 | 确认两端都是9600 |

## 📱 移动端参考

### 拍照记录连接
建议拍照记录以下角度：
1. 整体连接俯视图
2. STM32端连接特写
3. OpenMV端连接特写  
4. OLED端连接特写

### 连接标签
在每根线上贴标签：
- 黄线：UART_TX
- 绿线：UART_RX
- 蓝线：I2C_SCL
- 紫线：I2C_SDA
- 红线：VCC_3V3
- 黑线：GND

---

**💡 专业提示：**
- 连接前先断电
- 使用万用表测试连通性
- 保存连接照片备用
- 遇到问题先检查GND连接
