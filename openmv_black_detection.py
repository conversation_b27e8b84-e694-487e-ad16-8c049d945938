# OpenMV黑色色块识别和串口通信程序
# 功能：识别黑色色块，计算其中心x坐标与视野中心x坐标的差值，并通过串口发送给STM32

import sensor, image, time, pyb
from pyb import UART

# 初始化摄像头
sensor.reset()
sensor.set_pixformat(sensor.RGB565)    # 设置像素格式为RGB565
sensor.set_framesize(sensor.QVGA)      # 设置分辨率为QVGA (320x240)
sensor.skip_frames(time = 2000)        # 跳过前2秒的帧，让摄像头稳定
sensor.set_auto_gain(False)            # 关闭自动增益
sensor.set_auto_whitebal(False)        # 关闭自动白平衡

# 初始化串口 (UART3, 波特率9600)
uart = UART(3, 9600)

# 定义黑色阈值 (L, A, B)
# L: 亮度 (0-100, 越小越暗)
# A: 绿红色彩 (-128到127)  
# B: 蓝黄色彩 (-128到127)
black_threshold = (0, 30, -128, 127, -128, 127)  # 黑色阈值

# 视野中心x坐标
center_x = 160  # QVGA分辨率下的中心x坐标 (320/2 = 160)

# 最小色块面积阈值
min_area = 100

# LED指示灯
red_led = pyb.LED(1)    # 红色LED
green_led = pyb.LED(2)  # 绿色LED

def send_data_to_stm32(x_diff):
    """
    发送x值差数据到STM32
    数据包格式: 0xFF + 数据高字节 + 数据低字节 + 保留字节1 + 保留字节2 + 0xFE
    """
    # 确保x_diff在有效范围内 (-320 到 320)
    if x_diff > 320:
        x_diff = 320
    elif x_diff < -320:
        x_diff = -320
    
    # 将有符号16位整数转换为无符号字节
    if x_diff < 0:
        x_diff_unsigned = 65536 + x_diff  # 转换为无符号16位
    else:
        x_diff_unsigned = x_diff
    
    high_byte = (x_diff_unsigned >> 8) & 0xFF    # 高字节
    low_byte = x_diff_unsigned & 0xFF            # 低字节
    
    # 构建数据包
    packet = bytearray([0xFF, high_byte, low_byte, 0x00, 0x00, 0xFE])
    
    # 发送数据包
    uart.write(packet)
    
    print("发送数据: x_diff =", x_diff, "数据包:", [hex(b) for b in packet])

def main():
    print("OpenMV黑色色块识别程序启动")
    print("视野中心x坐标:", center_x)
    print("黑色阈值:", black_threshold)
    print("最小色块面积:", min_area)
    
    clock = time.clock()
    
    while(True):
        clock.tick()
        img = sensor.snapshot()  # 拍摄一帧图像
        
        # 查找黑色色块
        blobs = img.find_blobs([black_threshold], pixels_threshold=min_area, area_threshold=min_area)
        
        if blobs:
            # 找到最大的黑色色块
            largest_blob = max(blobs, key=lambda b: b.pixels())
            
            # 获取色块中心坐标
            blob_center_x = largest_blob.cx()
            blob_center_y = largest_blob.cy()
            
            # 计算x值差 (色块中心x - 视野中心x)
            x_diff = blob_center_x - center_x
            
            # 在图像上绘制色块边框和中心点
            img.draw_rectangle(largest_blob.rect(), color=(255, 0, 0))  # 红色边框
            img.draw_cross(blob_center_x, blob_center_y, color=(0, 255, 0))  # 绿色十字
            
            # 在图像上显示x值差
            img.draw_string(10, 10, "X Diff: %d" % x_diff, color=(255, 255, 255))
            img.draw_string(10, 30, "Blob X: %d" % blob_center_x, color=(255, 255, 255))
            img.draw_string(10, 50, "Area: %d" % largest_blob.pixels(), color=(255, 255, 255))
            
            # 发送数据到STM32
            send_data_to_stm32(x_diff)
            
            # 点亮绿色LED表示检测到色块
            green_led.on()
            red_led.off()
            
        else:
            # 没有检测到黑色色块
            img.draw_string(10, 10, "No black blob", color=(255, 0, 0))
            
            # 发送x值差为0
            send_data_to_stm32(0)
            
            # 点亮红色LED表示没有检测到色块
            red_led.on()
            green_led.off()
        
        # 在图像上绘制视野中心线
        img.draw_line(center_x, 0, center_x, 240, color=(0, 0, 255))  # 蓝色中心线
        
        # 显示帧率
        print("FPS:", clock.fps())
        
        # 短暂延时
        time.sleep_ms(50)  # 50ms延时，约20FPS

if __name__ == "__main__":
    main()
