#include "stm32f10x.h"
#include "Serial.h"
#include <stdio.h>
#include <stdarg.h>

// 全局变量
static uint8_t Serial_RxFlag = 0;        // 接收完成标志
static int16_t Serial_ReceivedData = 0;  // 接收到的x值差数据
static uint8_t RxBuffer[PACKET_SIZE];    // 接收缓冲区
static uint8_t RxIndex = 0;              // 接收索引
static RxState_t RxState = RX_STATE_IDLE; // 接收状态

/**
  * @brief  串口初始化
  * @param  无
  * @retval 无
  */
void Serial_Init(void)
{
    // 开启时钟
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);
    
    // GPIO配置
    GPIO_InitTypeDef GPIO_InitStructure;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_9;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_10;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_Init(GPIOA, &GPIO_InitStructure);
    
    // USART配置
    USART_InitTypeDef USART_InitStructure;
    USART_InitStructure.USART_BaudRate = 9600;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_Init(USART1, &USART_InitStructure);
    
    // 中断配置
    USART_ITConfig(USART1, USART_IT_RXNE, ENABLE);
    
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    
    NVIC_InitTypeDef NVIC_InitStructure;
    NVIC_InitStructure.NVIC_IRQChannel = USART1_IRQn;
    NVIC_InitStructure.NVIC_IRQChannelCmd = ENABLE;
    NVIC_InitStructure.NVIC_IRQChannelPreemptionPriority = 1;
    NVIC_InitStructure.NVIC_IRQChannelSubPriority = 1;
    NVIC_Init(&NVIC_InitStructure);
    
    // 使能USART
    USART_Cmd(USART1, ENABLE);
}

/**
  * @brief  串口发送一个字节
  * @param  Byte 要发送的字节
  * @retval 无
  */
void Serial_SendByte(uint8_t Byte)
{
    USART_SendData(USART1, Byte);
    while (USART_GetFlagStatus(USART1, USART_FLAG_TXE) == RESET);
}

/**
  * @brief  串口发送数组
  * @param  Array 要发送的数组
  * @param  Length 数组长度
  * @retval 无
  */
void Serial_SendArray(uint8_t *Array, uint16_t Length)
{
    uint16_t i;
    for (i = 0; i < Length; i++)
    {
        Serial_SendByte(Array[i]);
    }
}

/**
  * @brief  串口发送字符串
  * @param  String 要发送的字符串
  * @retval 无
  */
void Serial_SendString(char *String)
{
    uint16_t i;
    for (i = 0; String[i] != '\0'; i++)
    {
        Serial_SendByte(String[i]);
    }
}

/**
  * @brief  次方函数
  * @param  X 底数
  * @param  Y 指数
  * @retval X的Y次方
  */
uint32_t Serial_Pow(uint32_t X, uint32_t Y)
{
    uint32_t Result = 1;
    while (Y--)
    {
        Result *= X;
    }
    return Result;
}

/**
  * @brief  串口发送数字
  * @param  Number 要发送的数字
  * @param  Length 数字长度
  * @retval 无
  */
void Serial_SendNumber(uint32_t Number, uint8_t Length)
{
    uint8_t i;
    for (i = 0; i < Length; i++)
    {
        Serial_SendByte(Number / Serial_Pow(10, Length - i - 1) % 10 + '0');
    }
}

/**
  * @brief  获取串口接收标志位
  * @param  无
  * @retval 接收标志位
  */
uint8_t Serial_GetRxFlag(void)
{
    return Serial_RxFlag;
}

/**
  * @brief  获取串口接收的数据
  * @param  无
  * @retval 接收的数据
  */
int16_t Serial_GetReceivedData(void)
{
    return Serial_ReceivedData;
}

/**
  * @brief  清除串口接收标志位
  * @param  无
  * @retval 无
  */
void Serial_ClearRxFlag(void)
{
    Serial_RxFlag = 0;
}

/**
  * @brief  USART1中断函数
  * @param  无
  * @retval 无
  */
void USART1_IRQHandler(void)
{
    static uint8_t data_high, data_low;
    
    if (USART_GetITStatus(USART1, USART_IT_RXNE) == SET)
    {
        uint8_t RxData = USART_ReceiveData(USART1);
        
        switch (RxState)
        {
            case RX_STATE_IDLE:
                if (RxData == PACKET_HEADER)
                {
                    RxState = RX_STATE_HEADER;
                    RxIndex = 0;
                    RxBuffer[RxIndex++] = RxData;
                }
                break;
                
            case RX_STATE_HEADER:
                data_high = RxData;
                RxBuffer[RxIndex++] = RxData;
                RxState = RX_STATE_DATA_HIGH;
                break;
                
            case RX_STATE_DATA_HIGH:
                data_low = RxData;
                RxBuffer[RxIndex++] = RxData;
                RxState = RX_STATE_DATA_LOW;
                break;
                
            case RX_STATE_DATA_LOW:
                RxBuffer[RxIndex++] = RxData;  // 保留字节1
                RxState = RX_STATE_RESERVED1;
                break;
                
            case RX_STATE_RESERVED1:
                RxBuffer[RxIndex++] = RxData;  // 保留字节2
                RxState = RX_STATE_RESERVED2;
                break;
                
            case RX_STATE_RESERVED2:
                if (RxData == PACKET_FOOTER)
                {
                    RxBuffer[RxIndex++] = RxData;
                    
                    // 数据包接收完成，解析数据
                    uint16_t temp_data = (data_high << 8) | data_low;
                    
                    // 转换为有符号16位整数
                    if (temp_data > 32767)
                    {
                        Serial_ReceivedData = temp_data - 65536;
                    }
                    else
                    {
                        Serial_ReceivedData = temp_data;
                    }
                    
                    Serial_RxFlag = 1;  // 设置接收完成标志
                }
                RxState = RX_STATE_IDLE;  // 重置状态
                RxIndex = 0;
                break;
                
            default:
                RxState = RX_STATE_IDLE;
                RxIndex = 0;
                break;
        }
        
        USART_ClearITPendingBit(USART1, USART_IT_RXNE);
    }
}
